import { <PERSON>cco<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Menu } from '@sapiens/insuredpro-ui-component-library';
import { AlertBar, DynamicType, PagePlaceholderIds, useTranslation } from '@sapiens/workbench-feature-library';
import { Row } from '@tanstack/react-table';
import { useParams } from 'react-router-dom';

import MenuPanel from '@/components/PageLayout/LeftSidePanel/MenuPanel';
import PageLayout from '@/components/PageLayout/PageLayout';
import PageLayoutGridWrapper from '@/components/PageLayout/PageLayoutGridWrapper';
import { ActivityLogPanel } from '@/components/PageLayout/RightSidePanel/ActivityLogPanel';
import BreadcrumbsPanel from '@/components/Pages/Overview/BreadcrumbsPanel';
import WorkpadItemDetailsForm from '@/components/Pages/Workpad/components/ItemDetails/WorkpadItemDetailsForm';
import { DynamicWidget } from '@/components/Widget/DynamicWidget';
import useGetParty from '@/pages/AccountItems/hooks/useGetParty';

import { AccountItemsTable } from '../../components/Pages/Workpad/components/AccountItems/AccountItemsTable';
import WorkpadEditableTable from './components/WorkpadEditableTable';

export const Workpad = () => {
  const { t } = useTranslation('fsw');
  const routeParams = useParams();
  const { partyId } = useGetParty({ ...routeParams });

  // Event handlers for the editable table
  const handleRowSelection = (row: Row<DynamicType>) => {
    console.log('📋 Selected workpad:', row.original.workpadTitle);
  };

  const handleDuplicate = (workpadNo: number) => {
    console.log('📄 Duplicating workpad #:', workpadNo);
    // Here you would implement the duplication logic
  };

  const handleCreateWorkpad = () => {
    console.log('➕ Creating new workpad');
    // Here you would implement the create workpad logic
  };

  const handleSave = () => {
    console.log('💾 Saving workpad changes');
    // Here you would implement the save logic
  };

  const handlePostItems = () => {
    console.log('📤 Posting workpad items');
    // Here you would implement the post items logic
  };

  return (
    <PageLayout
      leftSidePanel={<Menu primaryMenuOpen={true} primaryMenuContent={<MenuPanel />} breakpoint="md" />}
      rightSidePanel={<ActivityLogPanel partyId={partyId || ''} householdId={''} paymentNo={''} />}
      rightSidebarProps={{ background: 'white', collapsedWidth: 0, brakepoint: 'xl' }}
      widgetTop={<DynamicWidget placeholderId={PagePlaceholderIds.fswWorkpadPageTop} />}
      widgetBottom={<DynamicWidget placeholderId={PagePlaceholderIds.fswWorkpadPageBottom} />}
      breadcrumb={<BreadcrumbsPanel wrapper={(children: React.ReactNode) => <PageLayoutGridWrapper>{children}</PageLayoutGridWrapper>} />}
      alertBar={<AlertBar wrapper={(children: React.ReactNode) => <PageLayoutGridWrapper>{children}</PageLayoutGridWrapper>} />}
    >
      <Box p={2}>
        <Card
          title={t('pages.workpad') || ''}
          isCollapsible={false}
          disablePaddingBottom={true}
          headerElement={
            <Box display="flex" marginLeft="auto" gap="10px">
              <Button spacing="tight" color="secondary">
                {t('workpad.authorization')}
              </Button>
            </Box>
          }
        >
          <>
            <Accordion title={t('workpad.workpadsAccordion') || ''} variant="clean" defaultExpanded={true}>
              <WorkpadEditableTable
                onRowSelection={handleRowSelection}
                onDuplicate={handleDuplicate}
                onCreateWorkpad={handleCreateWorkpad}
                onSave={handleSave}
                onPostItems={handlePostItems}
              />
            </Accordion>

            <Accordion title={t('workpad.accountItemsAccordion') || ''} variant="clean" defaultExpanded={true}>
              <AccountItemsTable />
              <Box display="flex" flexDirection="column" alignItems="flex-end" mt={2} pr={2}>
                <Box fontSize="1rem">{t('workpad.workpadBalance') || 'Workpad balance'}</Box>
                <Box fontSize="1.2rem">EUR 3,045.93</Box>
              </Box>
            </Accordion>

            <Accordion title={t('workpad.accountItemsDetails') || ''} variant="clean" defaultExpanded={true}>
              <WorkpadItemDetailsForm formId="workpad-form" />
            </Accordion>
          </>
        </Card>
      </Box>
    </PageLayout>
  );
};
