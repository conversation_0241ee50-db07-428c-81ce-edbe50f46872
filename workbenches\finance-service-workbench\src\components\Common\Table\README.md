# SingleRowEditTable for Finance Service Workbench

A comprehensive table component that supports double-click row editing, inline editing, and server-side data management.

## Features

✅ **Double-Click Row Editing**: Double-click any row to enter edit mode
✅ **Inline Editing**: Edit fields directly in the table
✅ **Server-Side Data**: Supports pagination, sorting, and filtering
✅ **Form Integration**: Built-in form handling with validation
✅ **Add New Rows**: Support for adding new records
✅ **Single Row Edit Mode**: Only one row can be edited at a time

## Basic Usage

```typescript
import { SingleRowEditProvider, SingleRowEditTable } from '@/components/Common/Table';

const MyTable = () => {
  const columns = [
    {
      accessorKey: 'name',
      header: 'Name',
      cell: (cellContext) => (
        <EditableCell
          cellContext={cellContext}
          fieldName="name"
          placeholder="Enter name"
        />
      )
    }
  ];

  return (
    <SingleRowEditProvider compareRowIds={(id1, id2) => id1 === id2}>
      <SingleRowEditTable
        columns={columns}
        url="/api/data"
        tableId="my-table"
        options={{
          showToolbar: true,
          enableRowSelection: true,
          noRecordsText: "No records found"
        }}
        enableDoubleClickEdit={true}
        onRowDoubleClick={(row) => console.log('Double clicked:', row)}
      />
    </SingleRowEditProvider>
  );
};
```

## Components

### SingleRowEditProvider
Provides context for managing edit state across the table.

**Props:**
- `compareRowIds`: Function to compare row IDs
- `customPayload`: Optional function to customize save payload
- `disableEditCallback`: Optional function to disable editing for specific rows
- `disableDeleteCallback`: Optional function to disable deletion for specific rows

### SingleRowEditTable
Main table component with editing capabilities.

**Props:**
- `columns`: Column definitions
- `url`: API endpoint for data
- `tableId`: Unique identifier for the table
- `options`: Table configuration options
- `enableDoubleClickEdit`: Enable double-click to edit (default: false)
- `onRowDoubleClick`: Callback for double-click events
- `onRowSelection`: Callback for row selection
- `addNewRowConfig`: Configuration for adding new rows

### EditableCell
Component for creating editable table cells.

**Props:**
- `cellContext`: Table cell context
- `fieldName`: Form field name
- `type`: Input type ('text' | 'number')
- `placeholder`: Input placeholder text

## Hooks

### useEditableField
Determines if a field should be editable based on current edit mode.

```typescript
const isEditable = useEditableField(cellContext);
```

### useSingleRowEdit
Access edit mode state and controls.

```typescript
const { setEditMode, clearEditMode, getEditMode } = useSingleRowEdit();
```

## Example: Workpad Table

See `WorkpadEditableTable.tsx` for a complete example of how to implement an editable workpad table with:
- Double-click editing
- Inline form fields
- Action buttons (Edit/Save/Cancel)
- Custom column definitions

## Integration with Existing Code

To replace your current workpad table with the editable version:

1. Import the WorkpadEditableTable component
2. Replace the existing DataTable with WorkpadEditableTable
3. Pass the appropriate URL and event handlers
4. Customize columns as needed

```typescript
// Replace this:
<DataTable columns={columns} data={data} />

// With this:
<WorkpadEditableTable 
  url="/api/workpads"
  onRowDoubleClick={(row) => console.log('Editing:', row)}
/>
```
