import { Input } from '@sapiens/insuredpro-ui-component-library';
import React, { useEffect, useState } from 'react';

interface EditableCellProps {
  value: string | number;
  onChange: (value: string | number) => void;
  type?: 'text' | 'number';
  placeholder?: string;
}

export const EditableCell: React.FC<EditableCellProps> = ({ value, onChange, type = 'text', placeholder }) => {
  const [localValue, setLocalValue] = useState(value);

  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setLocalValue(newValue);

    if (type === 'number') {
      const numValue = parseInt(newValue) || 0;
      onChange(numValue);
    } else {
      onChange(newValue);
    }
  };

  return (
    <InputFormField
      size="small"
      type={type}
      value={localValue}
      onChange={handleChange}
      placeholder={placeholder}
      fullWidth
      inputProps={{
        style: { padding: '8px' }
      }}
    />
  );
};
