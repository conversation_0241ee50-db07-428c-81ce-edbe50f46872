import { DataTable, DynamicType } from '@sapiens/workbench-feature-library';
import { ColumnDef, Row } from '@tanstack/react-table';
import { useCallback, useMemo } from 'react';

import { useSingleRowEdit } from './SingleRowEdit/SingleRowEditProvider';
import TableFormProvider, { TableFormProviderProps } from './TableFormProvider';
import { ServerSideEditTableOptions, useServerSideEditTable } from './useServerSideEditTable';

type SingleRowEditTableProps<T> = {
  columns: ColumnDef<T>[];
  url: string;
  tableId: string;
  options: Partial<ServerSideEditTableOptions>;
  addNewRowConfig?: {
    newButtonLabel: string;
  };
  onRowSelection?: (row: Row<DynamicType>) => void;
};

const SingleRowEditTable = <T extends object>({
  columns,
  url,
  tableId,
  options,
  addNewRowConfig,
  onRowSelection
}: SingleRowEditTableProps<T>) => {
  const { clearEditMode, setEditMode } = useSingleRowEdit();

  const discardCallback = useCallback(() => {
    clearEditMode();
  }, [clearEditMode]);

  const addNewRowCallback = useCallback(() => {
    setEditMode(undefined, 'newRow');
  }, [setEditMode]);

  const handleRowClick = (row: Row<DynamicType>) => {
    row.toggleSelected(true);
    onRowSelection?.(row);
  };

  const {
    columns: tableColumns,
    componentProps,
    data: tableData,
    context,
    setContext,
    methods
  } = useServerSideEditTable({
    url,
    columns,
    options,
    tableId,
    discardCallback,
    addNewRowConfig: addNewRowConfig
      ? {
          newButtonLabel: addNewRowConfig.newButtonLabel,
          callback: addNewRowCallback,
          disabled: false
        }
      : undefined
  });

  const tableFormProviderProps: Omit<TableFormProviderProps, 'children'> = useMemo(
    () => ({
      methods,
      context,
      setContext
    }),
    [methods, context, setContext]
  );

  return (
    <TableFormProvider {...tableFormProviderProps}>
      <DataTable columns={tableColumns} data={tableData} tableId={tableId} {...componentProps} onRowClick={handleRowClick} />
    </TableFormProvider>
  );
};

export default SingleRowEditTable;
export type { SingleRowEditTableProps };
