{"name": "@sapiens/cypress-utilities", "private": true, "version": "0.0.0", "type": "module", "scripts": {"build": "tsc && vite build", "lint": "eslint ./ --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@types/oracledb": "^6.0.3", "cypress": "^13.4.0", "cypress-keycloak": "^2.0.1", "oracledb": "^6.2.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "cypress": "^13.4.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.0.2", "vite": "^4.4.5", "vite-plugin-dts": "^3.5.3", "vite-plugin-eslint": "^1.8.1", "vite-plugin-externalize-deps": "^0.7.0", "vite-plugin-svgr": "^3.2.0"}, "files": ["dist"], "main": "./dist/cypress-utilities.umd.js", "module": "./dist/cypress-utilities.es.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/cypress-utilities.es.js", "require": "./dist/cypress-utilities.umd.js"}}}